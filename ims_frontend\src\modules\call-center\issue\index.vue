<template>
  <div class="issues-management">
    <!-- Issue Form -->
    <card v-if="showIssueForm" class="mt-0">
      <template slot="header">
        <h4 class="card-title">
          {{
            isEditing
              ? `Edit Issue (${currentIssue.issueRef}) [ source: ${currentIssue.source} ]`
              : "Add Issue"
          }}
        </h4>
      </template>
      <el-dialog
        title="Create Issue"
        :visible.sync="showIssueForm"
        width="80%"
        append-to-body="true"
        top="10px"
        draggable
        @close="resetCurrentIssue"
      >
        <form @submit.prevent="submitIssue" :model="currentIssue">
          <div class="row">
            <div class="col-md-4">
              <fg-input label="Issue Type">
                <el-select
                  v-model="currentIssue.issueType"
                  placeholder="Select"
                  @change="setDetails"
                >
                  <el-option
                    key="customer"
                    label="Customer related"
                    value="customer"
                  />

                  <el-option
                    key="noncustomer"
                    label="Non customer related"
                    value="noncustomer"
                  />
                </el-select>
              </fg-input>
            </div>
          </div>
          <div class="row" v-if="currentIssue.issueType == 'customer'">
            <div class="col-md-3">
              <fg-input label="Search customer by">
                <el-select
                  v-model="searchBy"
                  placeholder="Select"
                  filterable
                  @change="onCustomerSelect"
                >
                  <el-option
                    key="customerid"
                    label="Customer Id"
                    value="customerid"
                  />
                  <el-option key="account" label="Account" value="account" />
                  <!-- Static options -->
                  <el-option key="name" label="Name" value="name" />
                  <el-option key="email" label="Email" value="email" />
                  <el-option key="phone" label="Phone" value="phone" />
                  <el-option
                    key="companyname"
                    label="Company"
                    value="companyname"
                  />
                </el-select>
              </fg-input>
            </div>
            <div class="col-md-4">
              <fg-input :label="searchBy">
                <el-input
                  v-model="searchInput"
                  :placeholder="'Enter ' + searchBy"
                  filterable
                  class="w-100"
                  required
                />
              </fg-input>
            </div>
          </div>
          <div class="row" v-if="currentIssue.issueType == 'customer'">
            <div class="col-md-2">
              <button
                class="btn btn-fill btn-warning btn-round btn-wd mb-4"
                @click="searchCustomer"
                :disabled="btnloading"
              >
                <span v-if="!btnloading">Search</span>
                <span v-else>
                  <i class="fas fa-spinner fa-spin"></i> Searching...
                </span>
              </button>
            </div>
          </div>

          <!-- Dropdown for search results (when searching by name) -->

          <div
            class="row"
            v-if="
              searchResults.length > 0 && currentIssue.issueType == 'customer'
            "
          >
            <div class="col-md-6">
              <fg-input label="Select Customer">
                <el-select
                  v-model="selectedCustomer"
                  placeholder="Select customer"
                  filterable
                  class="w-100"
                  @change="onCustomerSelect"
                >
                  <el-option
                    v-for="customer in searchResults"
                    :key="customer.i_customer"
                    :label="`${customer.name} (${customer.email})`"
                    :value="customer.i_customer"
                  />
                </el-select>
              </fg-input>
            </div>
          </div>

           <!-- Replace the existing unresolved issues table section -->
           <div class="row mt-3" v-if="customerUnresolvedIssues.length > 0">
            <div class="col-md-12">
              <div class="alert alert-warning">
                <div class="d-flex justify-content-between align-items-center mb-2">
                  <h6 class="mb-0">⚠️ Customer has {{ customerUnresolvedIssues.length }} unresolved issue(s)</h6>
                  <button class="btn btn-sm btn-outline-dark" @click="customerUnresolvedIssues = []">
                    Clear All
                  </button>
                </div>
                <div class="table-responsive">
                  <table class="table table-sm table-bordered mb-0 table-hover">
                    <thead class="bg-light">
                      <tr>
                        <th>Issue ID</th>
                        <th>Subject</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th>Department</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="issue in customerUnresolvedIssues"
                          :key="issue.issueId"
                          @click="navigateToIssue(issue.issueId)"
                          style="cursor: pointer;">
                        <td>{{ issue.issueRef }}</td>
                        <td>{{ issue.title }}</td>
                        <td><span class="badge badge-warning">{{ issue.status }}</span></td>
                        <td>{{ formatDateTime(issue.reportedAt) }}</td>
                        <td>{{ getDepartment(issue.assignedDepartmentId) }}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
          <div class="row" v-if="currentIssue.issueType == 'customer'">
            <div class="col-md-4">
              <fg-input :label="`Customer ID`">
                <el-input
                  v-model="currentIssue.customerId"
                  placeholder="Select customer"
                  filterable
                  class="w-100"
                  readonly
                  disabled
                  required
                />
              </fg-input>
            </div>
            <div class="col-md-4">
              <fg-input :label="`Contact person`">
                <el-input
                  v-model="currentIssue.customer"
                  filterable
                  class="w-100"
                  required
                />
              </fg-input>
            </div>
            <div class="col-md-4">
              <fg-input :label="`Company`">
                <el-input
                  v-model="currentIssue.company"
                  filterable
                  class="w-100"
                  required
                />
              </fg-input>
            </div>
          </div>

            <div
            class="row"
            v-if="accounts.length > 0 && currentIssue.issueType == 'customer'"
          >
            <div class="col-md-4">
              <fg-input label="Select Account">
                <el-select
                  v-model="currentIssue.account"
                  placeholder="Select account"
                  filterable
                  class="w-100"
                  @change="onAccountSelect"
                >
                  <el-option
                    v-for="account in accounts"
                    :key="account.id"
                    :label="account.id"
                    :value="account.id"
                  />
                </el-select>
              </fg-input>
            </div>
            <div class="col-md-4" v-if="selectedProduct || loadingProduct">
              <fg-input label="Product" readonly>
                <el-input
                  :value="productDisplayValue"
                  placeholder="Product will be auto-selected"
                  class="w-100"
                  readonly
                >
                  <template slot="suffix" v-if="loadingProduct">
                    <i class="fas fa-spinner fa-spin"></i>
                  </template>
                </el-input>
              </fg-input>
            </div>
              <div class="col-md-4" v-if="selectedProduct  || loadingProduct">
              <fg-input label="Service" readonly>
                <el-select
                  v-model="currentIssue.serviceId"
                  placeholder="Select service"
                  filterable
                  class="w-100"
                  required
                  readonly
                >
                  <el-option
                    v-for="service in services"
                    :key="service.i_service_type"
                    :label="`${service.name} - ${service.i_service_type}`"
                    :value="service.i_service_type"
                  />
                </el-select>
              </fg-input>
            </div>
          </div>

          <div class="row">
            <div class="col-md-12">
              <fg-input label="Subject" required>
                <input
                  v-model="currentIssue.title"
                  type="text"
                  class="form-control"
                  placeholder="Enter issue title"
                  required
                />
              </fg-input>
            </div>
          </div>
          <div class="row" v-if="currentIssue.issueType == 'customer'">
             <div class="col-md-6">
              <fg-input label="Fault Date & Time" required>
                <el-date-picker
                  v-model="currentIssue.faultStartdate"
                  type="datetime"
                  placeholder="Select fault date and time"
                  format="dd/MM/yyyy HH:mm"
                  :default-value="new Date()"
                  :picker-options="{
                    disabledDate(time) {
                      return time.getTime() > Date.now();
                    },
                  }"
                />
              </fg-input>
            </div>
            <div class="col-md-6">
              <fg-input label="Category">
                <el-select
                  v-model="currentIssue.category"
                  placeholder="Select category"
                  class="w-100"
                  required
                >
                  <el-option
                    v-for="cat in issueCategories"
                    :key="cat"
                    :label="cat"
                    :value="cat"
                  />
                </el-select>
              </fg-input>
            </div>
          </div>

          <div class="row" v-if="currentIssue.issueType == 'customer'">
            <div class="col-md-6">
              <fg-input label="Priority">
                <el-select
                  v-model="currentIssue.priority"
                  placeholder="Select priority"
                  class="w-100"
                  required
                >
                  <el-option
                    v-for="priority in issuePriorities"
                    :key="priority"
                    :label="priority"
                    :value="priority"
                  />
                </el-select>
              </fg-input>
            </div>
            <div class="col-md-6">
              <fg-input label="Status">
                <el-select
                  v-model="currentIssue.status"
                  placeholder="Select status"
                  class="w-100"
                  :disabled="true"
                >
                  <el-option
                    v-for="status in issueStatuses"
                    :key="status"
                    :label="status"
                    :value="status"
                  />
                </el-select>
              </fg-input>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <fg-input label="Phone" required>
                <input
                  v-model="currentIssue.phone"
                  type="tel"
                  class="form-control"
                  placeholder="Enter phone"
                  required
                />
              </fg-input>
            </div>
            <div class="col-md-6">
              <fg-input label="Email">
                <input
                  v-model="currentIssue.email"
                  type="email"
                  :readonly="currentIssue.issueType == 'noncustomer'"
                  :disabled="currentIssue.issueType == 'noncustomer'"
                  class="form-control"
                  placeholder="Enter email"
                />
              </fg-input>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <fg-input label="District" required>
                <el-select
                  v-model="currentIssue.district"
                  placeholder="Select district"
                  class="w-100"
                  required
                >
                  <el-option
                    v-for="district in malawiDistricts"
                    :key="district"
                    :label="district"
                    :value="district"
                  />
                </el-select>
              </fg-input>
            </div>
            <div class="col-md-6">
              <fg-input label="Location" required>
                <input
                  v-model="currentIssue.location"
                  type="text"
                  class="form-control"
                  placeholder="Enter location"
                  required
                />
              </fg-input>
            </div>
          </div>

          <div class="row">
            <div class="col-md-12">
              <fg-input label="Description">
                <textarea
                  v-model="currentIssue.description"
                  class="form-control"
                  rows="4"
                  placeholder="Enter issue description"
                ></textarea>
              </fg-input>
            </div>
          </div>

          <!-- Department Assignment -->
          <div class="row">
            <div class="col-md-6">
              <fg-input label="Assign Department">
                <el-select
                  v-model="currentIssue.assignedDepartmentId"
                  placeholder="Select department"
                  class="w-100"
                >
                  <el-option
                    v-for="department in departments"
                    :key="department.departmentId"
                    :label="department.name"
                    :value="department.departmentId"
                  />
                </el-select>
              </fg-input>
            </div>
          </div>

          <div class="row">
            <div class="col-md-12 d-flex justify-content-between">
              <div>
                <button
                  type="submit"
                  class="btn btn-fill btn-info"
                  :disabled="isSaving || isClosing"
                >
                  <span v-if="!isSaving">
                    {{ isEditing ? "Update Issue" : "Save Issue" }}
                  </span>
                  <span v-else>
                    <i class="fas fa-spinner fa-spin"></i>
                    {{ isEditing ? "Updating..." : "Saving..." }}
                  </span>
                </button>
                <button
                  type="button"
                  class="btn btn-fill btn-secondary ml-2"
                  @click="closeIssueForm"
                  :disabled="isSaving || isClosing"
                >
                  Cancel
                </button>
              </div>
              <div v-if="currentIssue.status !== 'Closed'">
                <button
                  type="button"
                  class="btn btn-fill btn-danger"
                  @click="closeIssue"
                  :disabled="isSaving || isClosing"
                  style="margin-left: 20px;"
                >
                  <span v-if="!isClosing">
                    <i class="fas fa-times-circle mr-1"></i>
                    Close Issue
                  </span>
                  <span v-else>
                    <i class="fas fa-spinner fa-spin"></i>
                    Closing...
                  </span>
                </button>
              </div>
            </div>
          </div>
        </form>
      </el-dialog>
    </card>

    <!-- Issues List -->
    <card>
      <template slot="header">
        <div class="col-sm-12 d-flex align-items-center row">
          <div class="col-4"><h4 class="card-title">Issues List</h4></div>
          <div class="col-8 text-right">
            <button class="btn btn-primary mx-2" @click="showIssueForm = true">
              Add Issue
            </button>

            <router-link
              class="btn btn-danger mx-2"
              to="/call-center/front-issues"
            >
              Uncategorized issues({{ this.newFaults }})
            </router-link>

            <router-link
              to="/call-center/timeline"
              class="btn btn-success ml-2"
            >
              Issues Timeline
            </router-link>
          </div>
        </div>
      </template>

      <div>
        <div class="row col-sm-12">
          <div
            class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
          >
            <el-select
              v-model="pagination.perPage"
              placeholder="Per page"
              style="width: 200px"
              class="mb-3"
            >
              <el-option
                v-for="item in pagination.perPageOptions"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>

            <el-input
              type="search"
              v-model="searchQuery"
              placeholder="Search issues"
              style="width: 200px"
              aria-controls="datatables"
            />
          </div>
        </div>
        <!-- Add this before your table -->
        <div class="row mb-3 col-sm-12">
          <!-- Department Filter -->
          <div class="col-md-4">
            <label>Department</label>
            <div class="d-flex">
              <el-select
                v-model="filters.departmentId"
                :placeholder="defaultDepartmentName"
                class="w-100"
              >
                <el-option key="all" label="All Departments" :value="null" />
                <el-option
                  v-for="dept in filteredDepartments"
                  :key="dept.departmentId"
                  :label="dept.name"
                  :value="dept.departmentId"
                  :selected="dept.departmentId === loggedInUser?.departmentId"
                />
              </el-select>
              <button
                v-if="filters.departmentId"
                class="btn btn-link text-danger ml-2"
                @click="resetDepartmentFilter"
              >
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>

            <!-- Technician Filter -->
          <div class="col-md-3">
            <label>Technician</label>
            <div class="d-flex">
              <el-select
                v-model="filters.technicianId"
                placeholder="All Technicians"
                class="w-100"
                :loading="loadingTechnicians"
              >
                <el-option key="all" label="All Technicians" :value="null" />
                <el-option
                  v-for="tech in filteredTechnicians"
                  :key="tech.id"
                  :label="tech.fullName"
                  :value="tech.id"
                />
              </el-select>
              <button
                v-if="filters.technicianId"
                class="btn btn-link text-danger ml-2"
                @click="filters.technicianId = null"
              >
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>

          <!-- Status Filter -->
          <div class="col-md-2">
            <label>Status</label>
            <div class="d-flex">
              <el-select
                v-model="filters.status"
                placeholder="All Statuses"
                class="w-100"
              >
                <el-option key="all" label="All Statuses" :value="null" />
                <el-option
                  v-for="status in issueStatuses"
                  :key="status"
                  :label="status"
                  :value="status"
                />
              </el-select>
              <button
                v-if="filters.status"
                class="btn btn-link text-danger ml-2"
                @click="filters.status = null"
              >
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>

          <!-- Customer Filter -->
          <div class="col-md-3">
            <label>Customer</label>
            <div class="d-flex">
              <el-select
                v-model="filters.customerId"
                placeholder="All Customers"
                filterable
                class="w-100"
              >
                <el-option key="all" label="All Customers" :value="null" />
                <el-option
                  v-for="customer in filteredCustomers"
                  :key="customer.i_customer"
                  :label="customer.name"
                  :value="customer.i_customer"
                />
              </el-select>
              <button
                v-if="filters.customerId"
                class="btn btn-link text-danger ml-2"
                @click="filters.customerId = null"
              >
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>



          <!-- Date Range Filter -->
          <div class="col-md-6 my-2">
            <label>Date Range</label>
            <div class="d-flex">
              <el-date-picker
                v-model="filters.dateRange"
                type="daterange"
                range-separator="-"
                start-placeholder="All Dates"
                end-placeholder="End date"
                class="w-100"
              />
              <button
                v-if="filters.dateRange"
                class="btn btn-link text-danger ml-2"
                @click="filters.dateRange = null"
              >
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>

          <div class="col-md-2  mb-2 mt-4 pt-2" v-if="hasActiveFilters">
              <button class="btn btn-secondary" @click="resetFilters">
                Reset
              </button>
          </div>

             <!-- Add Reset All Filters button -->

        </div>



        <div class="col-sm-12">
          <el-table
            @row-click="handleRowClick"
            :data="queriedData"
            :default-sort="{ prop: 'issueRef', order: 'descending' }"
            stripe
            style="width: 100%"
          >
            <el-table-column
              v-for="column in tableColumns"
              :key="column.prop"
              :prop="column.prop"
              :label="column.label"
              :min-width="column.minWidth"
              :sortable="column.sortable"
              :formatter="column.formatter"
            />
            <el-table-column label="Assigned To" min-width="200" sortable>
              <template v-slot="{ row }">
                <div v-if="row?.assignedDepartmentId">
                  {{ getDepartment(row.assignedDepartmentId) }}
                </div>
              </template>
            </el-table-column>

            <!-- Actions Column -->
            <el-table-column label="Actions" width="150" :sortable="false">
              <template v-slot="{ row }">
                <router-link
                  class="btn btn-primary btn-sm mx-1"
                  :to="`/call-center/issue-view/${row.issueId}`"
                  @click.native.stop
                >
                  <i class="fa fa-eye"></i>
                </router-link>
                <button
                  class="btn btn-warning btn-sm mx-1"
                  v-if="
                    (!row?.assignedTechnicianId ||
                      !row?.assignedDepartmentId) &&
                    !isIssueClosed(row)
                  "
                  @click.stop="openTransferDialog(row)"
                >
                  <i class="fa-solid fa-arrows-up-down-left-right"></i>
                </button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- Pagination -->
      <div
        slot="footer"
        class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
      >
        <p class="card-category">
          Showing {{ from + 1 }} to {{ to }} of {{ total }} entries
        </p>
        <l-pagination
          v-model="pagination.currentPage"
          :per-page="pagination.perPage"
          :total="pagination.total"
          class="pagination-no-border"
        />
      </div>
    </card>

    <!-- Transfer Dialog -->
    <el-dialog
      title="Assign issue"
      :visible.sync="showTransferDialog"
      width="400px"
      @close="resetTransferForm"
    >
      <el-form :model="transferForm" label-width="120px">
        <el-form-item label="Department">
          <el-select
            v-model="transferForm.departmentId"
            placeholder="Select department"
            @change="loadTechnicians"
          >
            <el-option
              v-for="dept in departments"
              :key="dept.departmentId"
              :label="dept.name"
              :value="dept.departmentId"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="Responsible">
          <el-select
            v-model="transferForm.technicianId"
            placeholder="Select technician/manager"
            :loading="loadingTechnicians"
          >
            <el-option
              v-for="tech in departmentTechnicians"
              :key="tech.id"
              :label="tech.fullName"
              :value="tech.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="Comment">
          <el-input
            v-model="transferForm.comment"
            type="textarea"
            placeholder="Comment"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showTransferDialog = false">Cancel</el-button>
        <el-button
          type="primary"
          :loading="transferring"
          @click="handleTransfer"
        >
          Confirm Assignment
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  FormGroupInput as FgInput,
  Pagination as LPagination,
} from "src/components/index";
import moment from "moment";
import {
  Select,
  Option,
  Button,
  Table,
  TableColumn,
  Dialog,
  Form,
  FormItem,
  Input,
  DatePicker,
  Tooltip,
} from "element-ui";
import API from "@/services/api";

export default {
  name: "IssuesManagement",
  components: {
    LPagination,
    "el-select": Select,
    "el-option": Option,
    "el-table": Table,
    "el-table-column": TableColumn,
    "el-input": Input,
    ElDialog: Dialog,
    ElSelect: Select,
    ElOption: Option,
    "el-date-picker": DatePicker,
  },
  data() {
    return {
      users: [],
      // Services for APIs
      issueService: new API(process.env.VUE_APP_API_URL, "issues"),
      customerService: new API(process.env.VUE_APP_API_URL, "customers"),
      serviceService: new API(process.env.VUE_APP_API_URL, "services"),
      departmentService: new API(process.env.VUE_APP_API_URL, "departments"),
      assignmentService: new API(process.env.VUE_APP_API_URL, "assignments"),
      teamMemberService: new API(process.env.VUE_APP_API_URL, "users"),
      reportService: new API(process.env.VUE_APP_API_URL, "report"),
      validateUserService: new API(
        process.env.VUE_APP_API_URL,
        "auth/validate-customer"
      ),
      searchBy: "name",
      selectedCustomer: null,
      searchByValueCustomerService: new API(
        process.env.VUE_APP_API_URL,
        "customers/search"
      ),
      searchByIdCustomerService: new API(
        process.env.VUE_APP_API_URL,
        "customers"
      ),
      searchByAccountsByCustomerIdService: new API(
        process.env.VUE_APP_API_URL,
        "accounts"
      ),
      searchByAccountCustomerService: new API(
        process.env.VUE_APP_API_URL,
        "customers/account"
      ),
      productService: new API(
        process.env.VUE_APP_API_URL,
        "Customers/Product"
      ),
      btnloading: false,
      searchResults: [],
      searchQuery: "",
      newFaults: 0,
      pagination: {
        perPage: 20,
        currentPage: 1,
        perPageOptions: [20, 40, 60],
        total: 0,
      },
      loading: false,
      // Status Filter
      selectedStatus: "New", // Default to show only "New" issues
      issueStatuses: ["New", "In Progress", "Resolved", "On Hold", "Closed"], // All statuses
      visibleStatuses: ["New", "In Progress", "Unassigned"], // Statuses visible in the dropdown by default

      // Table Columns
      tableColumns: [
        { prop: "issueRef", label: "Issue ID", minWidth: 150, sortable: true },
        { prop: "title", label: "Subject", minWidth: 200, sortable: true },
        { prop: "customer", label: "Customer", minWidth: 140, sortable: true },
        { prop: "product", label: "Product", minWidth: 140, sortable: true },
        { prop: "status", label: "Status", minWidth: 100, sortable: true },
        {
          prop: "reportedAt",
          label: "Created",
          minWidth: 150,
          sortable: true,
          formatter: this.formatDateTime,
        },
        {
          prop: "issueId",
          label: "Downtime",
          minWidth: 120,
          sortable: false,
          formatter: (row) => {
            const downtimeInfo = this.getIssueDowntime(row.issueId);
            return downtimeInfo?.downtime || "N/A";
          },
        },
      ],

      // Dropdown Options
      issueCategories: [
        "Service Requests",
        "Incidents",
        "Problems",
        "Changes",
        "Billing Issues",
        "call-center Support",
        "Customer Feedback",
        "Account Management",
      ],
      issuePriorities: ["Lowest", "Low", "Medium", "High", "Highest"],
      // Data Arrays
      issues: [],
      customers: [],
      accounts: [],
      services: [],
      departments: [],
      assignments: [],
      searchInput: "",
      // Current Issue for Form
      currentIssue: {
        issueId: null,
        customerId: null,
        serviceId: null,
        product: null, // Add product field
        title: "",
        description: "",
        category: "",
        priority: "Low",
        status: "New",
        district: "",
        assignedDepartmentId: null,
        issueType: "customer",
        faultStartdate: "", // Add this line
      },

      // Form Control
      showIssueForm: false,
      isEditing: false,

      // Malawi Districts
      malawiDistricts: [
        "Balaka",
        "Blantyre",
        "Chikwawa",
        "Chiradzulu",
        "Chitipa",
        "Dedza",
        "Dowa",
        "Karonga",
        "Kasungu",
        "Likoma",
        "Lilongwe",
        "Machinga",
        "Mangochi",
        "Mchinji",
        "Mulanje",
        "Mwanza",
        "Mzimba",
        "Neno",
        "Nkhata Bay",
        "Nkhotakota",
        "Nsanje",
        "Ntcheu",
        "Ntchisi",
        "Phalombe",
        "Rumphi",
        "Salima",
        "Thyolo",
        "Zomba",
      ],
      isSaving: false,
      isClosing: false,
      filters: {
        departmentId: this.loggedInUser?.departmentId, // Set initial value to user's department
        status: null,
        customerId: null,
        dateRange: null,
        technicianId: null, // Add technician filter
      },
      showTransferDialog: false,
      transferring: false,
      loadingTechnicians: false,
      departmentTechnicians: [],
      selectedIssue: null,
      transferForm: {
        departmentId: null,
        technicianId: null,
        comment: "",
      },
      issuesLogService: new API(process.env.VUE_APP_API_URL, "issuelogs"),
      issueLogs: {}, // Cache for issue logs
      customerUnresolvedIssues: [], // Add this new property
      selectedProduct: null, // Store selected product information
      loadingProduct: false, // Loading state for product API call
    };
  },
  computed: {
    isSupervisor() {
      return JSON.parse(localStorage.getItem("isSupervisor"));
    },
    loggedInUser() {
      return JSON.parse(localStorage.getItem("user"));
    },
    userId() {
      return this.loggedInUser?.id;
    },
    customerId() {
      return this.loggedInUser?.account;
    },
    email() {
      return this.loggedInUser?.email;
    },
    phone() {
      return this.loggedInUser?.phoneNumber;
    },
    company() {
      return this.loggedInUser?.department;
    },
    filteredDepartments() {
      const userDepartment = this.loggedInUser?.departmentId;
      // Create a new array instead of mutating the original
      return [...this.departments].sort((a, b) => {
        if (a.departmentId === userDepartment) return -1;
        if (b.departmentId === userDepartment) return 1;
        return a.name.localeCompare(b.name);
      });
    },

    filteredIssues() {
      let filtered = [...this.issues];
      const userDepartmentId = this.loggedInUser?.departmentId;

      // Apply department filter logic
      if (this.filters.departmentId !== null) {
        // If specific department is selected
        filtered = filtered.filter(
          (issue) => issue.assignedDepartmentId === this.filters.departmentId
        );
      } else {
        // If "All Departments" is selected (null), show user's department by default
        const userDepartmentIssues = filtered.filter(
          (issue) => issue.assignedDepartmentId === userDepartmentId
        );

        // If user's department has no issues or "All Departments" is explicitly selected
        if (
          userDepartmentIssues.length === 0 ||
          this.filters.departmentId === null
        ) {
          filtered = this.issues; // Show all issues
        } else {
          filtered = userDepartmentIssues; // Show user's department issues
        }
      }

      // ...rest of your existing filter logic remains the same
      if (this.filters.status) {
        filtered = filtered.filter(
          (issue) => issue.status === this.filters.status
        );
      }

      if (this.filters.customerId) {
        filtered = filtered.filter(
          (issue) => issue.customerId === this.filters.customerId
        );
      }

      // Filter by technician
      if (this.filters.technicianId) {
        filtered = filtered.filter(
          (issue) => issue.assignedTechnicianId === this.filters.technicianId
        );
      }

      if (this.filters.dateRange && this.filters.dateRange.length === 2) {
        const [start, end] = this.filters.dateRange;
        filtered = filtered.filter((issue) => {
          const issueDate = new Date(issue.faultStartdate);
          return issueDate >= start && issueDate <= end;
        });
      }

      if (this.searchQuery) {
        filtered = filtered.filter((issue) =>
          Object.values(issue).some((value) =>
            value
              ?.toString()
              .toLowerCase()
              .includes(this.searchQuery.toLowerCase())
          )
        );
      }

      return filtered;
    },

    queriedData() {
      return this.filteredIssues.slice(
        (this.pagination.currentPage - 1) * this.pagination.perPage,
        this.pagination.currentPage * this.pagination.perPage
      );
    },

    total() {
      const totalItems = this.filteredIssues.length;
      // Update pagination total through a watcher instead
      return totalItems;
    },

    to() {
      let highBound = this.from + this.pagination.perPage;
      if (this.total < highBound) {
        highBound = this.total;
      }
      return highBound;
    },
    from() {
      return this.pagination.perPage * (this.pagination.currentPage - 1);
    },
    hasActiveFilters() {
      return (
        this.filters.departmentId ||
        this.filters.status ||
        this.filters.customerId ||
        this.filters.dateRange ||
        this.filters.technicianId
      );
    },
    filteredTechnicians() {
      // Filter technicians to only show those with usertype "internal"
      // and belonging to the selected department (if a department is selected)
      if (!this.users || this.users.length === 0) {
        return [];
      }

      let technicians = this.users.filter(user => user.userType === "internal");

      // If a department is selected, filter technicians by department
      if (this.filters.departmentId) {
        technicians = technicians.filter(tech => tech.departmentId === this.filters.departmentId);
      }

      return technicians;
    },
    defaultDepartmentName() {
      const userDept = this.departments.find(
        (d) => d.departmentId === this.loggedInUser?.departmentId
      );
      return userDept?.name || "All Departments";
    },

    hasUserDepartmentIssues() {
      if (this.isSupervisor) return true;
      const userDepartmentIssues = this.issues.filter(
        (issue) =>
          issue.assignedDepartmentId === this.loggedInUser?.departmentId
      );
      return userDepartmentIssues.length === 0;
    },
    filteredCustomers() {
      // Get unique customers directly from filtered issues
      const uniqueCustomers = this.issues.reduce((acc, issue) => {
        if (
          issue.customer &&
          issue.customerId &&
          !acc.some((c) => c.i_customer === issue.customerId)
        ) {
          acc.push({
            i_customer: issue.customerId,
            name: issue.customer,
          });
        }
        return acc;
      }, []);

      // Sort customers by name
      return uniqueCustomers.sort((a, b) => a.name.localeCompare(b.name));
    },

    productDisplayValue() {
      if (this.loadingProduct) {
        return 'Loading product...';
      }
      return this.selectedProduct ? this.selectedProduct.name : '';
    },
  },
  watch: {
    "filteredIssues.length"(newLength) {
      this.pagination.total = newLength;
    },
    'filters.departmentId': function(newDepartmentId) {
      // Reset technician filter when department changes
      this.filters.technicianId = null;
    },
  },
  methods: {
    getIssueDowntime(issueId) {
      const issue = this.issues.find((issue) => issue.issueId === issueId);

      if (!issue) {
        return null;
      }

      if (issue.status !== "Closed" && issue.status !== "Resolved") {
        const reportedTime = new Date(issue.reportedAt);
        const now = new Date();
        const downtimeMs = now - reportedTime;
        const downtimeHours = Math.floor(downtimeMs / (1000 * 60 * 60));
        const downtimeDays = Math.floor(downtimeHours / 24);

        return {
          id: issue.issueId,
          downtime:
            downtimeDays > 0
              ? `${downtimeDays}d ${downtimeHours % 24}h`
              : `${downtimeHours}h`,
        };
      }

      return {
        id: issue.issueId,
        downtime: "N/A",
      };
    },
    async fetchReports() {
      try {
        let newFaults = await this.reportService.getAll();
        this.newFaults = newFaults.filter((x) => x.status == "New")?.length | 0;
      } catch (error) {
        this.$alert.error("Failed to fetch Team Members");
      }
    },
    async fetchUsers() {
      try {
        this.loadingTechnicians = true;
        this.users = await this.teamMemberService.getAll();
      } catch (error) {
        this.$alert.error("Failed to fetch Users");
      } finally {
        this.loadingTechnicians = false;
      }
    },
    formatAssignee(assignment) {
      try {
        if (!assignment) return 'Not assigned';

        const technician = this.users.find(t => t.id === assignment.AssignedTo);
        if (!technician) return 'Unknown';

        return `${technician.fullName} (${this.getDepartment(technician.departmentId)})`;
      } catch (error) {
        console.error('Error formatting assignee:', error);
        return 'Error';
      }
    },

    async assignIssue(issueId, assigneeId, startDate) {
      try {
        await this.assignmentService.create({
          issueId: issueId,
          AssignedTo: assigneeId,
          assignedAt: new Date(),
          startDate: startDate,
          dueDate: this.dueDate,
        });

        await this.loadall();
        this.$alert.success("Issue assigned successfully");
      } catch (error) {
        console.error('Failed to assign issue:', error);
        this.$alert.error("Failed to assign issue");
      }
    },
    getIssue(issueId) {
      let issue = this.issues.find((x) => x.issueId == issueId);
      return issue;
    },
    async GetSLAHrsPriorty(issueId, start) {
      let issue = await this.getIssue(issueId);

      let result = await slaCalculator.getSLAHoursAndPriority(
        issue.customerId,
        issue.serviceId,
        start
      );

      this.slapriority = result.priority;
      this.sladuedate = this.getDateTimeWithoutSeconds(result.dueDate);
      this.dueDate = result.dueDate;
    },

    getDateTimeWithoutSeconds(datestring) {
      return moment(datestring).format("YYYY-MM-DD hh:mm");
    },

    getAssignment(issueId) {
      return this.assignments.find((a) => a.issueId === issueId);
    },
    async fetchAccountByCustomerId(_customer) {
      this.btnloading = true;
      try {
        let customer = await _customer;
        //console.log(customer.i_customer)
        const input = customer.i_customer;
        if (!input) {
          this.$alert.warning("Please enter a customer Id");
          return;
        }

        //console.log(input)
        const response = await this.searchByAccountsByCustomerIdService.getById(
          input
        );
        this.accounts = response;

        // Auto-select first account and its service if only one account exists
        if (this.accounts && this.accounts.length === 1) {
          const firstAccount = this.accounts[0];
          this.currentIssue.account = firstAccount.id;

          // Trigger product/service auto-selection for the first account
          if (firstAccount.i_product) {
            await this.fetchProductByAccount(firstAccount.i_product);
          }
        }
      } catch (error) {
        //console.log(error);
        this.$alert.error(
          error.response?.data?.message || "Failed to find accounts"
        );
      } finally {
        this.btnloading = false;
      }
    },
    async searchCustomer() {
      this.btnloading = true;
      this.accounts=[]
      try {
        const input = this.searchInput.trim();
        if (!input) {
          this.$alert.warning("Please enter a customer " + searchBy);
          return;
        }

        // Check if input is a number (ID or Account)
        if (this.searchBy == "customerid") {
          // Search by ID or Account
          const response = await this.searchByIdCustomerService.getById(input);
          this.populateCustomerDetails(response);
          this.fetchAccountByCustomerId(response);
        } else if (this.searchBy == "account") {
          // Search by ID or Account
          const response = await this.searchByAccountCustomerService.getById(
            input
          );
          this.populateCustomerDetails(response);
          // For account search, also fetch accounts and auto-select the searched account
          await this.fetchAccountByCustomerId(response);
          if (this.accounts && this.accounts.length > 0) {
            // Find and auto-select the account that was searched for
            const searchedAccount = this.accounts.find(acc => acc.id === input);
            if (searchedAccount) {
              this.currentIssue.account = searchedAccount.id;
              await this.onAccountSelect(searchedAccount.id);
            }
          }
        } else {
          // Search by Name
          const response = await this.searchByValueCustomerService.getById(
            input + "_" + this.searchBy
          );
          this.searchResults = response;

          if (response && response.length > 0) {
            this.$alert.success("Customers found");
          }
        }
      } catch (error) {
        //console.log(error);
        this.$alert.error(
          error.response?.data?.message || "Failed to search for customer"
        );
      } finally {
        this.btnloading = false;
      }
    },

    onCustomerSelect(customerId) {
      this.accounts=[]
      this.currentIssue.account=null;
      const customer = this.searchResults.find(
        (c) => c.i_customer === customerId
      );
      if (customer) {
        this.populateCustomerDetails(customer);
        this.fetchAccountByCustomerId(customer);
      }
    },

    async onAccountSelect(accountId) {
      if (!accountId) {
        this.selectedProduct = null;
        this.currentIssue.product = null;
        return;
      }

      // Find the selected account to get its product ID
      const selectedAccount = this.accounts.find(account => account.id === accountId);
      if (!selectedAccount || !selectedAccount.i_product) {
        this.$alert.warning("No product information found for this account");
        return;
      }

      try {
        await this.fetchProductByAccount(selectedAccount.i_product);
      } catch (error) {
        console.error("Error in account selection:", error);
        this.$alert.error("Failed to load product information");
      }
    },

    async fetchProductByAccount(productId) {
      if (!productId) {
        this.selectedProduct = null;
        return;
      }

      this.loadingProduct = true;
      try {
        const productResponse = await this.productService.getById(productId);

        if (productResponse) {
          this.selectedProduct = {
            name: productResponse.name || productResponse.end_user_name || "Unknown Product",
            description: productResponse.description || productResponse.end_user_description || "",
            productGroup: productResponse.product_group_name,
            productId: productResponse.i_product,
            includedServices: productResponse.included_services || []
          };

          // Assign product value to currentIssue.product
          this.currentIssue.product = this.selectedProduct.name;

          // Auto-assign the first service from included_services
          if (this.selectedProduct.includedServices && this.selectedProduct.includedServices.length > 0) {
            const primaryServiceId = this.selectedProduct.includedServices[0];
            this.currentIssue.serviceId = primaryServiceId;

            // Show success message with product and service info
            const serviceName = this.getServiceName(primaryServiceId);
            this.$alert.success(`Product "${this.selectedProduct.name}" selected. Service auto-assigned: ${serviceName}`);
          } else {
            this.$alert.warning(`Product "${this.selectedProduct.name}" selected, but no services are included. Please select a service manually.`);
          }
        } else {
          this.selectedProduct = null;
          this.currentIssue.product = null;
          this.$alert.error("Product information not found");
        }
      } catch (error) {
        console.error("Error fetching product:", error);
        this.selectedProduct = null;
        this.currentIssue.product = null;
        this.$alert.error(
          error.response?.data?.message || "Failed to fetch product information"
        );
      } finally {
        this.loadingProduct = false;
      }
    },

    getServiceName(serviceId) {
      const service = this.services.find(s => s.i_service_type === serviceId);
      return service ? service.name : `Service ID: ${serviceId}`;
    },

    setDetails() {
      if (this.currentIssue.issueType == "noncustomer") {
        let user = this.loggedInUser;
        this.currentIssue.customerId = "0001";
        this.currentIssue.customer = user.fullName;
        this.currentIssue.company = "MTL";
        this.currentIssue.serviceId = 2;
        this.currentIssue.email = user.email;

        this.currentIssue.phone = user.phoneNumber;
      } else {
        this.currentIssue.customerId = "";
        this.currentIssue.customer = "";
        this.currentIssue.company = "";

        this.currentIssue.email = "";

        this.currentIssue.phone = "";
      }
    },
    populateCustomerDetails(customer) {
      if (!customer) {
        this.$alert.error("No customer details found");
        return;
      }

      this.$alert.success("Customer found");

      // Populate all customer fields
      this.currentIssue = {
        ...this.currentIssue,
        customerId: customer.i_customer,
        customer: `${customer.firstname} ${customer.lastname}`,
        company: customer.name || customer.company,
        email: customer.email,
        phone: customer.phone1 || customer.phone2,
        location: customer.city || customer.address_line_1,
        district: customer.city || "",
      };

      // Check for unresolved issues
      this.customerUnresolvedIssues = this.getCustomerUnresolvedIssues(customer.i_customer);
      if (this.customerUnresolvedIssues.length > 0) {
        this.$alert.warning(`Customer has ${this.customerUnresolvedIssues.length} unresolved issues`);
      }

      // Clear search state
      this.searchResults = [];
      this.selectedCustomer = customer.i_customer;
    },

    async validateUser() {
      if (!this.currentIssue.Id) {
        this.$alert.error("Please fill in all required fields.");
        return;
      }
      this.btnloading = true; // Start loading
      try {
        // Call the backend validation endpoint
        const response = await this.validateUserService.create({
          name: "",
          account: this.currentIssue.Id,
          userType: "customer",
        });

        if (response.message == "Validation successful.") {
          this.$alert.success("Client found");

          const customerData = response.customer;

          //console.log(customerData);

          // Bind the customer data to the form fields

          this.currentIssue.customerId = customerData.i_customer; // Bind customer ID
          this.currentIssue.company =
            customerData.name ||
            customerData?.firstname + " " + customerData?.lastname; // Bind customer name
          this.currentIssue.phone = customerData.phone1 || customerData.phone2; // Bind phone
          this.currentIssue.email = customerData.email; // Bind email
          this.currentIssue.district = customerData.city; // Bind location
          this.currentIssue.location = customerData.address_line_2; // Bind location

          this.currentIssue.customer =
            customerData?.firstname + " " + customerData?.lastname;
        } else {
          this.$alert.error("Client not found");
        }
      } catch (error) {
        this.$alert.error(
          error.response?.data?.message ||
            "Error validating user. Please try again."
        );
      } finally {
        this.btnloading = false; // Stop loading
      }
    },
    handleRowClick(row, column, event) {
      // Check if click was on or inside action buttons
      if (event.target.closest(".btn")) {
        return;
      }
      this.$router.push(`/call-center/issue-view/${row.issueId}`);
    },
    formatDateTime(dateString) {
      return moment(dateString).format("D/M/YYYY");
    },
    formatCustomer(row, column, cellValue) {
      const customer = this.customers.find((c) => c.customerId === cellValue);
      return customer ? customer.name : "Unknown";
    },
    formatAccount(row, column, cellValue) {
      const customer = this.customers.find(
        (c) => c.customerId === row.customerId
      );
      return customer ? customer.account : "Unknown";
    },

    formatCustomerName(customer) {
      // Display full customer name without masking
      const fullName = `${customer.firstname} ${customer.lastname}`;
      return `${fullName} - ${customer.email || customer.phone1 || ""}`;
    },
    // Fetch Methods
    async fetchIssues() {
      this.loading = true;
      try {
        const response = await this.issueService.getAll();
        this.issues = response || [];
        this.pagination.total = this.issues.length || 0;
      } catch (error) {
        this.$alert.error(error.response?.message || "Failed to fetch Issues");
      } finally {
        this.loading = false;
      }
    },

    async fetchServices() {
      try {
        this.services = (await this.serviceService.getAll())?.services ?? [];
      } catch (error) {
        this.$alert.error("Failed to fetch Services");
      }
    },
    async fetchDepartments() {
      try {
        const response = await this.departmentService.getAll();
        this.departments = Array.isArray(response) ? response : [];

        this.departments = this.departments.reduce((acc, current) => {
          // Check if the department name already exists in the accumulator
          const isDuplicate = acc.some((item) => item.name === current.name);
          // If not a duplicate, add it to the accumulator
          if (!isDuplicate) {
            acc.push(current);
          }
          return acc;
        }, []);
        //console.log(this.departments);
      } catch (error) {
        this.$alert.error("Failed to fetch Departments");
      }
    },
    resetCurrentIssue() {
      this.customerUnresolvedIssues = []; // Add this line
      this.selectedProduct = null; // Reset selected product
      this.accounts = []; // Reset accounts
      this.searchResults = []; // Reset search results
      this.currentIssue = {
        issueId: null,
        customerId: null,
        serviceId: null,
        product: null, // Reset product field
        title: "",
        description: "",
        category: "Other",
        priority: "Low",
        status: "New",
        district: "",
        assignedDepartmentId: null,
        faultStartdate: new Date(), // Add this line
      };
    },
    editIssue(row) {
      this.currentIssue = { ...row };
      this.isEditing = true;
      this.showIssueForm = true;
    },
    closeIssueForm() {
      this.resetCurrentIssue();
      this.isEditing = false;
      this.showIssueForm = false;
    },
    async submitIssue() {
      if (this.isSaving) return; // Prevent double submission
      this.isSaving = true;
      try {
        // Ensure department assignment
        if (!this.currentIssue.assignedDepartmentId) {
          const defaultDepartment = this.departments.find((d) =>
            d.name.toLowerCase().includes("customer service")
          );
          if (defaultDepartment) {
            this.currentIssue.assignedDepartmentId =
              defaultDepartment.departmentId;
          } else {
            throw new Error("No default department found for auto-assignment");
          }
        }

        if (this.isEditing) {
          await this.updateIssue();
        } else {
          await this.addIssue();
        }
      } catch (error) {
        this.$alert.error(error.message || "Failed to submit issue");
      } finally {
        this.isSaving = false;
      }
    },
    async closeIssue() {
      //if (this.isClosing) return; // Prevent double submission

      // Confirm close action
      const confirmed = await this.$confirm(
        'Are you sure you want to close this issue? This action cannot be undone.',
        'Confirm Close Issue',
        {
          confirmButtonText: 'Yes, Close Issue',
          cancelButtonText: 'Cancel',
          type: 'warning',
          confirmButtonClass: 'btn-danger'
        }
      ).catch(() => false);

      if (!confirmed) return;

      this.isClosing = true;
      try {
        // Auto-assign department and technician if not already assigned
        if (!this.currentIssue.assignedDepartmentId) {
          this.currentIssue.assignedDepartmentId = this.loggedInUser?.departmentId;
        }
        if (!this.currentIssue.assignedTechnicianId) {
          this.currentIssue.assignedTechnicianId = this.loggedInUser?.id;
        }

        // Set issue status to closed
        this.currentIssue.status = "Closed";
        this.currentIssue.visibleToDepartment = false;
        this.currentIssue.isClosed = true;

        // Update the issue
        await this.issueService.update(
          this.currentIssue.issueId,
          this.currentIssue
        );

        // Create issue log entry
        await this.issuesLogService.create({
          issueId: this.currentIssue.issueId,
          actionTaken: "Issue Closed",
          comment: `Issue closed by ${this.loggedInUser?.fullName}. Status: Closed`,
          status: "Closed",
          updatedAt: moment().format(),
          updatedBy: this.userId,
          expose: false
        });

        // Close any pending assignments
        const pendingAssignments = this.assignments.filter(
          (a) => a.issueId === this.currentIssue.issueId && !a.closedAt
        );

        await Promise.all(
          pendingAssignments.map((assignment) =>
            this.assignmentService.update(assignment.id, {
              ...assignment,
              closedAt: new Date(),
              status: "Closed",
            })
          )
        );

        this.$alert.success("Issue closed successfully");
        this.closeIssueForm();
        await this.loadall();
      } catch (error) {
        this.$alert.error(
          error.response?.data?.message || "Failed to close issue"
        );
      } finally {
        this.isClosing = false;
      }
    },
    async addIssue() {
      try {
        const newObject = { ...this.currentIssue };
        delete newObject.issueId;

        // Set assignment date if department is assigned
        if (newObject.assignedDepartmentId) {
          newObject.assignedDate = moment().format()
        }

        newObject.source = this.company;
        newObject.status = "New";
        newObject.visibleToDepartment = true;

        // Create the issue
        const response = await this.issueService.create(newObject);

        // Create issue creation log
        await this.issuesLogService.create({
          issueId: response.issueId,
          actionTaken: "Issue Created",
          comment: "Issue created via "+this.company,
          status: "New",
          updatedAt: moment().format(),
          updatedBy: this.userId,
          expose: false
        });

        // If department is assigned, create department assignment log
        if (response.assignedDepartmentId) {
          await this.issuesLogService.create({
            issueId: response.issueId,
            actionTaken: `Assigned to ${this.getDepartment(response.assignedDepartmentId)}`,
            comment: "Initial department assignment",
            status: "Assigned",
            updatedAt: moment().format(),
            updatedBy: this.userId,
            expose: false
          });

          // Auto-assign to technician if department is set
          await this.autoAssignTechnician(response.issueId, response.assignedDepartmentId);
        }

        this.$alert.success("Issue added successfully");
        this.closeIssueForm();
        await this.loadall();
      } catch (error) {
        console.error(error);
        this.$alert.error(error.response?.data?.message || "Failed to add Issue");
      }
    },
    async updateIssue() {
      try {
        // Check if issue is being closed
        if (this.currentIssue.status === "Closed") {
          this.currentIssue.visibleToDepartment = false;
          this.currentIssue.isClosed = true;

          // Close any pending transfers or reassignments
          const pendingAssignments = this.assignments.filter(
            (a) => a.issueId === this.currentIssue.issueId && !a.closedAt
          );

          await Promise.all(
            pendingAssignments.map((assignment) =>
              this.assignmentService.update(assignment.id, {
                ...assignment,
                closedAt: new Date(),
                status: "Closed",
              })
            )
          );
        }

        await this.issueService.update(
          this.currentIssue.issueId,
          this.currentIssue
        );

        this.$alert.success("Issue updated successfully");
        this.closeIssueForm();
        await this.loadall();
      } catch (error) {
        this.$alert.error(
          error.response?.data?.message || "Failed to update Issue"
        );
      }
    },
    async autoAssignTechnician(issueId, departmentId) {
      try {
        const availableTechs = await this.teamMemberService.getAll({
          departmentId,
          role: "technical",
          status: "active",
        });

        if (availableTechs.length > 0) {
          // Simple round-robin assignment - you might want to implement more sophisticated logic
          const selectedTech = availableTechs[0];

          await this.assignmentService.create({
            issueId,
            assignedTo: selectedTech.id,
            assignedAt: new Date(),
            status: "Assigned",
          });
        }
      } catch (error) {
        console.error("Failed to auto-assign technician:", error);
        // Don't throw error as this is a non-critical operation
      }
    },
    async loadall() {
      await Promise.all([
        await this.fetchDepartments(),
        await this.fetchUsers(),
        this.fetchIssues(),
        this.fetchServices(),
        this.fetchDepartments(),
        this.fetchReports(),
        this.prefetchLogs(), // Add this line
      ]);
    },
    getDepartment(departmentId) {
      const department = this.departments.find(
        (d) => d.departmentId === departmentId
      );
      return department ? department.name : "Not assigned";
    },
    getTechnician(id) {
      const assignedTechnician = this.users.find((x) => x.id == id);
      return assignedTechnician ? assignedTechnician.fullName : "Not assigned";
    },
    resetFilters() {
      this.filters = {
        departmentId: null, // Reset to null to show user's department
        status: null,
        customerId: null,
        dateRange: null,
        technicianId: null,
      };
    },
    resetDepartmentFilter() {
      this.filters.departmentId = null; // Reset to null to show user's department
    },
    isIssueClosed(issue) {
      let issueFilter = this.issues.find(
        (x) => x.issueId == issue.issueId && x.status == "Closed"
      );
      return issueFilter ? true : false;
    },

    async getLastLog(issueId) {
      // Check cache first
      if (!this.issueLogs[issueId]) {
        try {
          const logs = await this.issuesLogService.getAll();
          const filteredLogs = logs.filter((x) => x.issueId === issueId);
          // Store in cache
          this.issueLogs[issueId] = filteredLogs;
        } catch (error) {
          console.error("Failed to fetch logs:", error);
          return null;
        }
      }

      const logs = this.issueLogs[issueId];
      if (!logs || logs.length === 0) return null;

      // Sort by date descending and return most recent
      return logs.sort(
        (a, b) => new Date(b.updatedAt) - new Date(a.updatedAt)
      )[0];
    },

    async prefetchLogs() {
      try {
        const logs = await this.issuesLogService.getAll();
        // Group logs by issueId for faster lookup
        this.issueLogs = logs.reduce((acc, log) => {
          if (!acc[log.issueId]) {
            acc[log.issueId] = [];
          }
          acc[log.issueId].push(log);
          return acc;
        }, {});
      } catch (error) {
        console.error("Failed to prefetch logs:", error);
      }
    },

    openTransferDialog(issue) {
      this.selectedIssue = issue;
      this.transferForm.departmentId = issue.assignedDepartmentId;
      this.showTransferDialog = true;
    },

    async loadTechnicians() {
      if (!this.transferForm.departmentId) return;

      this.loadingTechnicians = true;
      try {
        const response = this.teamMembers;
        this.departmentTechnicians = response.filter(
          (x) => x.departmentId == this.transferForm.departmentId
        );

        this.assignedTechnician = this.teamMembers.find(
          (x) => x.id == this.issue.assignedTechnicianId
        ).fullName;
      } catch (error) {
        //this.$alert.error("Failed to load technicians");
      } finally {
        this.loadingTechnicians = false;
      }
    },

    async handleTransfer() {
      if (!this.transferForm.departmentId || !this.transferForm.technicianId) {
        this.$alert.error(
          "Please select both department and technician/manager"
        );
        return;
      }

      this.transferring = true;
      try {
        // Update issue assignment
        await this.issueService.update(this.selectedIssue.issueId, {
          ...this.selectedIssue,
          assignedDepartmentId: this.transferForm.departmentId,
          assignedTechnicianId: this.transferForm.technicianId,
          assignedDate: new Date(),
        });

        let status = "Assigned";
        let text = "Assigned";
        try {
          text =
            "Assigned to " + this.getDepartment(this.transferForm.departmentId);
          if (
            this.selectedIssue.assignedDepartmentId > 0 &&
            this.selectedIssue.assignedDepartmentId !=
              this.transferForm.departmentId
          ) {
            status = "Transferred";
            text = `Issue Transferred from ${this.getDepartment(
              this.selectedIssue.assignedDepartmentId
            )} to ${this.getDepartment(this.transferForm.departmentId)}`;
          }
        } catch(ex) {
          console.log(ex)
        }

        // Create transfer log
        const logData = {
          issueId: this.selectedIssue.issueId,
          actionTaken: text,
          comment: this.transferForm.comment,
          status: status,
          updatedAt: new Date(),
          updatedBy: this.userId,
          expose: false,
        };

        await this.issuesLogService.create(logData);

        // Update UI
        await this.loadall();
        this.showTransferDialog = false;
        this.$alert.success("Issue successfully transferred");
      } catch (error) {
        this.$alert.error("Failed to assign the issue");
      } finally {
        this.transferring = false;
      }
    },

    resetTransferForm() {
      this.transferForm = {
        departmentId: null,
        technicianId: null,
        comment: "",
      };
      this.departmentTechnicians = [];
      this.selectedIssue = null;
    },
    getCustomerUnresolvedIssues(customerId) {
      return this.issues.filter(issue =>
        issue.customerId === customerId &&
        !['Closed', 'Resolved'].includes(issue.status)
      );
    },
    navigateToIssue(issueId) {
      this.$router.push(`/call-center/issue-view/${issueId}`);
    },
  },
  created() {
    // Set initial department filter
    this.filters.departmentId = this.loggedInUser?.departmentId;
    this.loadall();
  },
};
</script>
