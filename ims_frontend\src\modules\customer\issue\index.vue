<template>
  <div class="issues-management">
    <el-dialog
      :title="isEditing ? 'Edit Issue' : 'Add Issue'"
      :visible.sync="showIssueForm"
      width="80%"
      append-to-body="true"
      top="5px"
      draggable
      @close="closeIssueForm"
    >
      <form @submit.prevent="submitIssue">
        <div class="row">
          <div class="col-md-6">
            <fg-input label="Customer ID">
              <input
                v-model="currentIssue.customerId"
                filterable
                type="text"
                class="form-control"
                readonly
              />
            </fg-input>
          </div>
          <div class="col-md-6">
            <fg-input label="Company">
              <input
                v-model="currentIssue.company"
                filterable
                type="text"
                class="form-control"
                readonly
              />
            </fg-input>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6">
            <fg-input label="Fault Date & Time" required>
              <el-date-picker
                v-model="currentIssue.faultStartdate"
                type="datetime"
                placeholder="Select fault date and time"
                format="dd/MM/yyyy HH:mm"
                :default-value="new Date()"
                :picker-options="{
                  disabledDate(time) {
                    return time.getTime() > Date.now();
                  },
                }"
              />
            </fg-input>
          </div>
        </div>

        <div
          class="row"
          v-if="
            accounts.length > 0
          "
        >
          <div class="col-md-6">
            <fg-input label="Account">
              <el-select
                v-model="currentIssue.account"
                placeholder="Select account"
                filterable
                class="w-100"
              >
                <el-option
                    v-for="account in accounts"
                    :key="account.id"
                    :label="account.id"
                    :value="account.id"
                  />
              </el-select>
            </fg-input>
          </div>
        </div>

        <div class="row">
          <div class="col-md-12">
            <fg-input label="Subject" required>
              <input
                v-model="currentIssue.title"
                type="text"
                class="form-control"
                placeholder="Enter issue title"
                required
              />
            </fg-input>
          </div>
        </div>
        <div class="row">
          <div class="col-md-6">
            <fg-input label="Service">
              <el-select
                v-model="currentIssue.serviceId"
                placeholder="Select service"
                filterable
                class="w-100"
              >
                <el-option
                  v-for="service in services"
                  :key="service.i_service_type"
                  :label="`${service.name} - ${service.i_service_type}`"
                  :value="service.i_service_type"
                />
              </el-select>
            </fg-input>
          </div>

          <div class="col-md-6">
            <fg-input label="Category">
              <el-select
                v-model="currentIssue.category"
                placeholder="Select category"
                class="w-100"
              >
                <el-option
                  v-for="cat in issueCategories"
                  :key="cat"
                  :label="cat"
                  :value="cat"
                />
              </el-select>
            </fg-input>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6" v-if="false">
            <fg-input label="Priority">
              <el-select
                v-model="currentIssue.priority"
                placeholder="Select priority"
                class="w-100"
              >
                <el-option
                  v-for="priority in issuePriorities"
                  :key="priority"
                  :label="priority"
                  :value="priority"
                />
              </el-select>
            </fg-input>
          </div>
          <div class="col-md-6">
            <fg-input label="Status" readonly>
              <input
                v-model="currentIssue.status"
                class="form-control"
                value="New"
                readonly
              />
            </fg-input>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6">
            <fg-input label="Phone" required>
              <input
                v-model="currentIssue.phone"
                type="phone"
                class="form-control"
                placeholder="Enter phone"
                required
              />
            </fg-input>
          </div>
          <div class="col-md-6">
            <fg-input label="Email" required>
              <input
                v-model="currentIssue.email"
                type="email"
                class="form-control"
                placeholder="Enter email"
              />
            </fg-input>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6">
            <fg-input label="District" required>
              <el-select
                v-model="currentIssue.district"
                placeholder="Select district"
                class="w-100"
                required
              >
                <el-option
                  v-for="district in malawiDistricts"
                  :key="district"
                  :label="district"
                  :value="district"
                />
              </el-select>
            </fg-input>
          </div>
          <div class="col-md-6">
            <fg-input label="Location" required>
              <input
                v-model="currentIssue.location"
                type="text"
                class="form-control"
                placeholder="Enter location"
                required
              />
            </fg-input>
          </div>

        </div>

        <div class="row">
          <div class="col-md-12">
            <fg-input label="Description">
              <textarea
                v-model="currentIssue.description"
                class="form-control"
                rows="4"
                placeholder="Enter issue description"
              ></textarea>
            </fg-input>
          </div>
        </div>

        <div class="row">
          <div class="col-md-12">
            <button
              type="submit"
              class="btn btn-fill btn-info"
              :disabled="isSaving"
            >
              <span v-if="!isSaving">
                {{ isEditing ? "Update Issue" : "Save Issue" }}
              </span>
              <span v-else>
                <i class="fas fa-spinner fa-spin"></i> {{ isEditing ? "Updating..." : "Saving..." }}
              </span>
            </button>
            <button
              type="button"
              class="btn btn-fill btn-secondary ml-2"
              @click="closeIssueForm"
              :disabled="isSaving"
            >
              Close
            </button>
          </div>
        </div>
      </form>
    </el-dialog>

    <!-- Issues List -->
    <card>
      <template slot="header">
        <div class="d-flex align-items-center row">
          <div class="col-6"><h4 class="card-title">Issues List</h4></div>
          <div class="col-6 text-right">
            <button class="btn btn-primary mx-2" @click="showIssueForm = true">
              Add Issue
            </button>
          </div>
        </div>
      </template>

      <div>
        <div
          class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
        >
          <el-select
            v-model="pagination.perPage"
            placeholder="Per page"
            style="width: 200px"
            class="mb-3"
          >
            <el-option
              v-for="item in pagination.perPageOptions"
              :key="item"
              :label="item"
              :value="item"
            ></el-option>
          </el-select>

          <el-input
            type="search"
            v-model="searchQuery"
            placeholder="Search issues"
            style="width: 200px"
            aria-controls="datatables"
          />
        </div>

        <div class="col-sm-12">
          <el-table :data="queriedData" stripe border style="width: 100%">
            <el-table-column
              v-for="column in tableColumns"
              :key="column.prop"
              :prop="column.prop"
              :label="column.label"
              :min-width="column.minWidth"
              :formatter="column.formatter"
            ></el-table-column>
            <el-table-column label="Action" width="90">
              <template v-slot="{ row }">
                <router-link
                  class="btn btn-primary btn-sm mx-1"
                  :to="`/customer/issue-view/${row.issueId}`"
                >
                  <i class="fa fa-eye"></i>
                </router-link>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- Pagination -->
      <div
        slot="footer"
        class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
      >
        <p class="card-category">
          Showing {{ from + 1 }} to {{ to }} of {{ total }} entries
        </p>
        <l-pagination
          v-model="pagination.currentPage"
          :per-page="pagination.perPage"
          :total="pagination.total"
          class="pagination-no-border"
        >
        </l-pagination>
      </div>
    </card>
  </div>
</template>

<script>
import {
  FormGroupInput as FgInput,
  Pagination as LPagination,
} from "src/components/index";
import { Select, Option, Table, TableColumn, Input, Dialog, DatePicker } from "element-ui";
import API from "@/services/api";
import { slaCalculator } from "../../../services/slacalculator";
import moment from "moment";

export default {
  name: "IssuesManagement",
  components: {
    FgInput,
    LPagination,
    "el-select": Select,
    "el-option": Option,
    "el-table": Table,
    "el-table-column": TableColumn,
    "el-input": Input,
    ElDialog: Dialog,
    "el-date-picker": DatePicker,
  },
  data() {
    return {
      // Services for different APIs
      issueService: new API(process.env.VUE_APP_API_URL, "issues"),
      customerService: new API(process.env.VUE_APP_API_URL, "customers"),
      serviceService: new API(process.env.VUE_APP_API_URL, "services"),
      issuesLogService: new API(process.env.VUE_APP_API_URL, "issuelogs"), // Add this line
      assignmentService: new API(process.env.VUE_APP_API_URL, "assignments"),
      teamMemberService: new API(process.env.VUE_APP_API_URL, "users"),
      searchByAccountsByCustomerIdService: new API(
        process.env.VUE_APP_API_URL,
        "accounts"
      ),
      // Search and Pagination
      searchQuery: "",
      pagination: {
        perPage: 5,
        currentPage: 1,
        perPageOptions: [5, 10, 15],
        total: 0,
      },

      malawiDistricts: [
        "Balaka",
        "Blantyre",
        "Chikwawa",
        "Chiradzulu",
        "Chitipa",
        "Dedza",
        "Dowa",
        "Karonga",
        "Kasungu",
        "Likoma",
        "Lilongwe",
        "Machinga",
        "Mangochi",
        "Mchinji",
        "Mulanje",
        "Mwanza",
        "Mzimba",
        "Neno",
        "Nkhata Bay",
        "Nkhotakota",
        "Nsanje",
        "Ntcheu",
        "Ntchisi",
        "Phalombe",
        "Rumphi",
        "Salima",
        "Thyolo",
        "Zomba",
      ],

      // Table Columns with Formatters
      tableColumns: [
        {
          prop: "issueRef",
          label: "Issue ID",
          minWidth: 120,
        },
        {
          prop: "title",
          label: "Title",
          minWidth: 200,
        },
        {
          prop: "product",
          label: "Product",
          minWidth: 140,
        },
        {
          prop: "status",
          label: "Status",
          minWidth: 100,
          formatter: this.formatStatusForCustomer,
        },
      ],

      // Dropdown Options
      issueCategories: [
        "Service Requests",
        "Incidents",
        "Problems",
        "Changes",
        "Billing Issues",
        "Technical Support",
        "Customer Feedback",
        "Account Management",
      ],
      issuePriorities: ["Lowest", "Low", "Medium", "High", "Highest"],
      issueStatuses: ["New", "In Progress"],

      // Data Arrays
      issues: [],
      customers: [],
      services: [],
      assignments: [],
      teamMembers: [],
      accounts:[],
      // Current Issue for Form
      currentIssue: {
        issueId: null,
        customerId: null,
        serviceId: null,
        title: "",
        description: "",
        category: "",
        priority: "Medium",
        status: "New",
        faultStartdate: moment().format(),
      },
      dueDate: "",
      // Form Control
      showIssueForm: false,
      isEditing: false,
      showAssignmentModal: false,
      selectedAssignee: "",
      currentIssueForAssignment: null,
      isSaving: false,
    };
  },
  computed: {
    loggedInUser() {
      return JSON.parse(localStorage.getItem("user"));
    },
    customerId() {
      return this.loggedInUser?.account;
    },
    email() {
      return this.loggedInUser?.email;
    },
    name() {
      return this.loggedInUser?.fullName;
    },
    company() {
      return this.loggedInUser?.department;
    },
    phone() {
      return this.loggedInUser?.phoneNumber;
    },
    queriedData() {
      let filtered = this.issues.filter((issue) =>
        Object.values(issue).some((value) =>
          value
            ?.toString()
            .toLowerCase()
            .includes(this.searchQuery.toLowerCase())
        )
      );
      return filtered.slice(
        (this.pagination.currentPage - 1) * this.pagination.perPage,
        this.pagination.currentPage * this.pagination.perPage
      );
    },
    total() {
      return this.issues.length;
    },
    to() {
      let highBound = this.from + this.pagination.perPage;
      if (this.total < highBound) {
        highBound = this.total;
      }
      return highBound;
    },
    from() {
      return this.pagination.perPage * (this.pagination.currentPage - 1);
    },
  },

  methods: {
    // Map issue status for customer display
    mapStatusForCustomer(status) {
      const allowedStatuses = ['closed', 'in progress', 'resolved'];
      const normalizedStatus = status.toLowerCase();

      if (allowedStatuses.includes(normalizedStatus)) {
        return status.charAt(0).toUpperCase() + status.slice(1).toLowerCase();
      }

      // Map any other status to 'In progress'
      return 'In progress';
    },

    async fetchAccountByCustomerId(customerId) {
      this.btnloading = true;
      try {
        const input = customerId;
        if (!input) {
          this.$alert.warning("Please enter a customer Id");
          return;
        }
        const response = await this.searchByAccountsByCustomerIdService.getById(input);
        this.accounts = response

      } catch (error) {
        this.$alert.error(
          error.response?.data?.message || "Failed to find accounts"
        );
      } finally {
        this.btnloading = false;
      }
    },
    onDialogOpen() {
      const selectComponent = this.$refs.selectRef;
    },
    setTechnician() {
      const selectComponent = this.$refs.selectRef;
      //console.log(val)
    },
    // Formatting Methods for Table
    formatDateTime(row, column, cellValue) {
      if (!cellValue) return "";
      return new Date(cellValue).toLocaleString();
    },

    formatCustomer(row, column, cellValue) {
      const customer = this.customers.find((c) => c.customerId === cellValue);
      return customer ? `${customer.name}` : "Unknown Customer";
    },

    formatAccount(row, column, cellValue) {
      const customer = this.customers.find(
        (c) => c.customerId === row.customerId
      );
      return customer ? `${customer.account}` : "Unknown Customer";
    },

    formatService(row, column, cellValue) {
      const service = this.services.find((s) => s.serviceId === cellValue);
      return service ? service.serviceName : "Unknown Service";
    },

    formatStatusForCustomer(row, column, cellValue) {
      return this.mapStatusForCustomer(cellValue);
    },

    // Fetch Methods
    async fetchIssues() {
      try {
        const response = await this.issueService.getAll();

        this.issues = response.filter((x) => x.customerId == this.customerId);
        this.pagination.total = response.length;
      } catch (error) {
        this.$alert.error(error.response?.message || "Failed to fetch Issues");
      }
    },

    async fetchCustomers() {
      try {
        this.customers = await this.customerService.getAll();
      } catch (error) {
        this.$alert.error("Failed to fetch Customers");
      }
    },

    async fetchServices() {
      try {
        this.services = (await this.serviceService.getAll()).services;
        //console.log(this.services);
      } catch (error) {
        this.$alert.error("Failed to fetch Services");
      }
    },

    // async fetchAssignments() {
    //   try {
    //     this.assignments = await this.assignmentService.getAll();
    //   } catch (error) {
    //     this.$alert.error("Failed to fetch Assignments");
    //   }
    // },

    async fetchTeamMembers() {
      try {
        this.teamMembers = await this.teamMemberService.getAll();
      } catch (error) {
        this.$alert.error("Failed to fetch Team Members");
      }
    },

    // Assignment Methods
    getAssignment(issueId) {
      return this.assignments.find((a) => a.issueId === issueId);
    },

    formatAssignee(assignment) {
      const member = this.teamMembers.find(
        (m) => m.id === assignment.assignedTo
      );
      return member ? member.fullName : "Unknown";
    },

    showAssignModal(issue) {
      this.currentIssueForAssignment = issue;
      this.selectedAssignee = null;
      this.showAssignmentModal = true;
    },

    async assignIssue() {
      try {
        let startDate = moment().format();
        await this.GetSLAHrsPriorty(
          this.currentIssueForAssignment.issueId,
          startDate
        );
        await this.assignmentService.create({
          issueId: this.currentIssueForAssignment.issueId,
          AssignedTo: this.selectedAssignee,
          assignedAt: moment().format(),
          startDate,
          dueDate: this.dueDate,
        });

        //await this.fetchAssignments();
        this.showAssignmentModal = false;
        this.$alert.success("Issue assigned successfully");
      } catch (error) {
        this.$alert.error("Failed to assign issue");
      }
    },
    getIssue(issueId) {
      let issue = this.issues.find((x) => x.issueId == issueId);
      return issue;
    },
    async GetSLAHrsPriorty(issueId, start) {
      let issue = await this.getIssue(issueId);

      let result = await slaCalculator.getSLAHoursAndPriority(
        issue.customerId,
        issue.serviceId,
        start
      );

      this.slapriority = result.priority;
      this.sladuedate = this.getDateTimeWithoutSeconds(result.dueDate);
      this.dueDate = result.dueDate;
    },

    getDateTimeWithoutSeconds(datestring) {
      return moment(datestring).format("YYYY-MM-DD hh:mm");
    },
    // CRUD Methods
    async submitIssue() {
      if (this.isSaving) return; // Prevent double submission
      this.isSaving = true;
      try {
        if (this.isEditing) {
          await this.updateIssue();
        } else {
          await this.addIssue();
        }
      } finally {
        this.isSaving = false;
      }
    },

    async addIssue() {
      try {
        const newObject = { ...this.currentIssue };
        delete newObject.issueId;
        newObject.source = "Customer";
        newObject.issueType = "customer";

        // Create the issue
        const response = await this.issueService.create(newObject);

        // Create issue log entry
        await this.issuesLogService.create({
          issueId: response.issueId,
          actionTaken: "Issue Created",
          comment: `Issue submitted by customer ${this.name} from ${this.company}`,
          status: "New",
          updatedAt: moment().format(),
          updatedBy: this.loggedInUser?.id,
          expose: true
        });

        this.$alert.success("Issue added successfully");
        this.closeIssueForm();
        await this.loadall();
      } catch (error) {
        this.$alert.error(error.response?.data?.message || "Failed to add Issue");
      }
    },

    async updateIssue() {
      try {
        await this.issueService.update(this.currentIssue.issueId, this.currentIssue);
        await this.loadall();
        this.$alert.success("Issue updated successfully");
        this.closeIssueForm();
      } catch (error) {
        this.$alert.error(error.response?.data?.message || "Failed to update Issue");
      }
    },

    editIssue(issue) {
      this.currentIssue = { ...issue };
      this.isEditing = true;
      this.showIssueForm = true;
    },

    async loadall() {
      await Promise.all([
        this.fetchIssues(),
        this.fetchCustomers(),
        this.fetchServices(),
        //this.fetchAssignments(),
        this.fetchTeamMembers(),
        this.fetchAccountByCustomerId(this.customerId)
      ]);
    },

    closeIssueForm() {
      this.isEditing = false;
      this.showIssueForm = false;
      this.isSaving = false; // Reset saving state
      this.currentIssue = {
        issueId: null,
        customerId: null,
        serviceId: null,
        title: "",
        description: "",
        category: "Other",
        priority: "Medium",
        status: "New",
        faultStartdate: moment().format(),
      };
    },
  },

  async created() {
    await this.loadall();
    this.currentIssue.phone = this.phone;
    this.currentIssue.email = this.email;
  },
  beforeMount() {
    this.currentIssue.customerId = this.customerId;
  },
  watch: {
    showIssueForm: function (newVal, oldVal) {
      this.currentIssue.customerId = this.customerId;
      this.currentIssue.company = this.company;
      this.currentIssue.customer = this.name;
    },
    // Watch for changes in search query and pagination
    searchQuery() {
      this.pagination.currentPage = 1;
      this.fetchIssues();
    },
    "pagination.currentPage"() {
      this.fetchIssues();
    },
    "pagination.perPage"() {
      this.pagination.currentPage = 1;
      this.fetchIssues();
    },
  },
};
</script>

<style>
.el-select {
  width: 100%;
}

.el-dialog__body {
  padding: 20px;
}

.form-group label {
  font-weight: bold;
  margin-bottom: 10px;
}
</style>