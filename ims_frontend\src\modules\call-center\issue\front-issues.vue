<template>
  <div class="issues-management">
    <!-- Issue Form -->
    <card v-if="showIssueForm" class="mt-4">
      <template slot="header">
        <h4 class="card-title">
          {{
            isEditing
              ? `Categorize Issue (${currentIssue.issueRef}) [ source: ${currentIssue.source} ]`
              : "Add Issue"
          }}
        </h4>
      </template>
      <el-dialog
        title="Create Issue"
        :visible.sync="showIssueForm"
        width="80%"
        append-to-body="true"
        top="5px"
        draggable
        @close="resetCurrentIssue"
      >
        <form @submit.prevent="submitIssue" :model="currentIssue">
          <div class="row">
            <div class="col-md-3">
              <fg-input label="Search customer by">
                <el-select
                  v-model="searchBy"
                  placeholder="Select"
                  filterable
                  @change="onCustomerSelect"
                >
                  <el-option
                    key="customerid"
                    label="Customer Id"
                    value="customerid"
                  />
                  <el-option key="account" label="Account" value="account" />
                  <!-- Static options -->
                  <el-option key="name" label="Name" value="name" />
                  <el-option key="email" label="Email" value="email" />
                  <el-option key="phone" label="Phone" value="phone" />
                  <el-option
                    key="companyname"
                    label="Company"
                    value="companyname"
                  />
                </el-select>
              </fg-input>
            </div>
            <div class="col-md-4">
              <fg-input
                :label="
                  searchBy +
                  (currentIssue.customer
                    ? ' (' + currentIssue.customer + ')'
                    : '')
                "
              >
                <el-input
                  v-model="searchInput"
                  :placeholder="'Enter ' + searchBy"
                  filterable
                  class="w-100"
                  required
                />
              </fg-input>
            </div>
          </div>
          <div class="row">
            <div class="col-md-2">
              <button
                class="btn btn-fill btn-warning btn-round btn-wd mb-4"
                @click="searchCustomer"
                :disabled="btnloading"
              >
                <span v-if="!btnloading">Search</span>
                <span v-else>
                  <i class="fas fa-spinner fa-spin"></i> Searching...
                </span>
              </button>
            </div>
          </div>

          <!-- Dropdown for search results (when searching by name) -->

          <div class="row" v-if="searchResults.length > 0">
            <div class="col-md-6">
              <fg-input label="Select Customer">
                <el-select
                  v-model="selectedCustomer"
                  placeholder="Select customer"
                  filterable
                  class="w-100"
                  @change="onCustomerSelect"
                >
                  <el-option
                    v-for="customer in searchResults"
                    :key="customer.i_customer"
                    :label="`${customer.name} (${customer.email})`"
                    :value="customer.i_customer"
                  />
                </el-select>
              </fg-input>
            </div>
          </div>

          <div class="row" v-if="accounts.length > 0">
            <div class="col-md-4">
              <fg-input label="Select Account">
                <el-select
                  v-model="currentIssue.account"
                  placeholder="Select account"
                  filterable
                  class="w-100"
                  @change="onAccountSelect"
                >
                 <el-option
                    v-for="account in accounts"
                    :key="account.id"
                    :label="account.id"
                    :value="account.id"
                  />
                </el-select>
              </fg-input>
            </div>
            <div class="col-md-4" v-if="selectedProduct || loadingProduct">
              <fg-input label="Product" readonly>
                <el-input
                  :value="productDisplayValue"
                  placeholder="Product will be auto-selected"
                  class="w-100"
                  readonly
                >
                  <template slot="suffix" v-if="loadingProduct">
                    <i class="fas fa-spinner fa-spin"></i>
                  </template>
                </el-input>
              </fg-input>
            </div>
            <div class="col-md-4" v-if="selectedProduct || loadingProduct">
              <fg-input label="Service" readonly>
                <el-select
                  v-model="currentIssue.serviceId"
                  placeholder="Select service"
                  filterable
                  class="w-100"
                  required
                  readonly
                >
                  <el-option
                    v-for="service in services"
                    :key="service.i_service_type"
                    :label="`${service.name} - ${service.i_service_type}`"
                    :value="service.i_service_type"
                  />
                </el-select>
              </fg-input>
            </div>
          </div>

          <div class="row">
            <div class="col-md-4">
              <fg-input :label="`Customer ID`">
                <el-input
                  v-model="currentIssue.customerId"
                  placeholder="Select customer"
                  filterable
                  class="w-100"
                  readonly
                  disabled
                  required
                />
              </fg-input>
            </div>
            <div class="col-md-4">
              <fg-input :label="`Customer`">
                <el-input
                  v-model="currentIssue.customer"
                  filterable
                  class="w-100"
                  required
                />
              </fg-input>
            </div>
            <div class="col-md-4">
              <fg-input :label="`Company`">
                <el-input
                  v-model="currentIssue.company"
                  filterable
                  class="w-100"
                  required
                />
              </fg-input>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <fg-input label="Subject" required>
                <input
                  v-model="currentIssue.title"
                  type="text"
                  class="form-control"
                  placeholder="Enter issue title"
                  required
                />
              </fg-input>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6" v-if="!selectedProduct">
              <fg-input label="Service">
                <el-select
                  v-model="currentIssue.serviceId"
                  placeholder="Select service"
                  filterable
                  class="w-100"
                  required
                >
                  <el-option
                    v-for="service in services"
                    :key="service.i_service_type"
                    :label="`${service.name} - ${service.i_service_type}`"
                    :value="service.i_service_type"
                  />
                </el-select>
              </fg-input>
            </div>

            <div class="col-md-6">
              <fg-input label="Category">
                <el-select
                  v-model="currentIssue.category"
                  placeholder="Select category"
                  class="w-100"
                  required
                >
                  <el-option
                    v-for="cat in issueCategories"
                    :key="cat"
                    :label="cat"
                    :value="cat"
                  />
                </el-select>
              </fg-input>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6" v-if="false">
              <fg-input label="Priority">
                <el-select
                  v-model="currentIssue.priority"
                  placeholder="Select priority"
                  class="w-100"
                  required
                >
                  <el-option
                    v-for="priority in issuePriorities"
                    :key="priority"
                    :label="priority"
                    :value="priority"
                  />
                </el-select>
              </fg-input>
            </div>
            <div class="col-md-6">
              <fg-input label="Status">
                <el-select
                  v-model="currentIssue.status"
                  placeholder="Select status"
                  class="w-100"
                  :disabled="true"
                >
                  <el-option
                    v-for="status in issueStatuses"
                    :key="status"
                    :label="status"
                    :value="status"
                  />
                </el-select>
              </fg-input>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <fg-input label="Phone" required>
                <input
                  v-model="currentIssue.phone"
                  type="tel"
                  class="form-control"
                  placeholder="Enter phone"
                  required
                />
              </fg-input>
            </div>
            <div class="col-md-6">
              <fg-input label="Email">
                <input
                  v-model="currentIssue.email"
                  type="email"
                  class="form-control"
                  placeholder="Enter email"
                />
              </fg-input>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <fg-input label="District" required>
                <el-select
                  v-model="currentIssue.district"
                  placeholder="Select district"
                  class="w-100"
                  required
                >
                  <el-option
                    v-for="district in malawiDistricts"
                    :key="district"
                    :label="district"
                    :value="district"
                  />
                </el-select>
              </fg-input>
            </div>
            <div class="col-md-6">
              <fg-input label="Location" required>
                <input
                  v-model="currentIssue.location"
                  type="text"
                  class="form-control"
                  placeholder="Enter location"
                  required
                />
              </fg-input>
            </div>
          </div>

          <div class="row">
            <div class="col-md-12">
              <fg-input label="Description">
                <textarea
                  v-model="currentIssue.description"
                  class="form-control"
                  rows="4"
                  placeholder="Enter issue description"
                ></textarea>
              </fg-input>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <fg-input label="Fault Start Date">
                <el-date-picker
                  v-model="currentIssue.faultStartDate"
                  type="datetime"
                  placeholder="Select fault start date"
                  class="w-100"
                  required
                />
              </fg-input>
            </div>
          </div>

          <div class="row">
            <div class="col-md-12 d-flex justify-content-between">
              <div>
                <button
                  type="submit"
                  class="btn btn-fill btn-info"
                  :disabled="isSaving || isClosing"
                >
                  <span v-if="!isSaving">
                    {{ isEditing ? "Categorize Issue" : "Save Issue" }}
                  </span>
                  <span v-else>
                    <i class="fas fa-spinner fa-spin"></i>
                    {{ isEditing ? "Categorizing..." : "Saving..." }}
                  </span>
                </button>
                <button
                  type="button"
                  class="btn btn-fill btn-secondary ml-2"
                  @click="closeIssueForm"
                  :disabled="isSaving || isClosing"
                >
                  Cancel
                </button>
              </div>
              <div v-if="isEditing && currentIssue.status !== 'Closed'">
                <button
                  type="button"
                  class="btn btn-fill btn-danger"
                  @click="closeIssue"
                  :disabled="isSaving || isClosing"
                  style="margin-left: 20px;"
                >
                  <span v-if="!isClosing">
                    <i class="fas fa-times-circle mr-1"></i>
                    Close Issue
                  </span>
                  <span v-else>
                    <i class="fas fa-spinner fa-spin"></i>
                    Closing...
                  </span>
                </button>
              </div>
            </div>
          </div>
        </form>
      </el-dialog>
    </card>

    <!-- Issues List -->
    <card>
      <template slot="header">
        <div class="d-flex align-items-center row">
          <div class="col-6">
            <h4 class="card-title">Uncategorized issues</h4>
          </div>
          <div class="col-6 text-right">
            <router-link to="/call-center/issues" class="btn btn-warning mx-2">
              Issues
            </router-link>
            <router-link
              to="/call-center/timeline"
              class="btn btn-success mx-2"
            >
              Issues Timeline
            </router-link>
          </div>
        </div>
      </template>

      <div>
        <div class="col-12 d-flex justify-content-between flex-wrap">
          <el-select
            v-model="pagination.perPage"
            placeholder="Per page"
            style="width: 120px"
            class="mb-3"
          >
            <el-option
              v-for="item in pagination.perPageOptions"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>

          <el-input
            type="search"
            v-model="searchQuery"
            placeholder="Search issues"
            style="width: 200px"
            class="mb-3"
            aria-controls="datatables"
          />
        </div>

        <div class="col-sm-12">
          <el-table
            :data="queriedData"
            stripe
            border
            style="width: 100%"
            v-loading="loading"
          >
            <el-table-column
              v-for="column in tableColumns"
              :key="column.prop"
              :prop="column.prop"
              :label="column.label"
              :min-width="column.minWidth"
              :formatter="column.formatter"
            />
            <el-table-column label="Action" min-width="150">
              <template v-slot="{ row }">
                <button
                  v-if="row.status === 'New'"
                  class="btn btn-info btn-sm mx-1"
                  @click="editIssue(row)"
                >
                  Categorize Issue
                </button>
                <span v-else>{{ row.status }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- Pagination -->
      <div
        slot="footer"
        class="col-12 d-flex justify-content-center justify-content-sm-between flex-wrap"
      >
        <p class="card-category">
          Showing {{ from + 1 }} to {{ to }} of {{ total }} entries
        </p>
        <l-pagination
          v-model="pagination.currentPage"
          :per-page="pagination.perPage"
          :total="pagination.total"
          class="pagination-no-border"
        />
      </div>
    </card>
  </div>
</template>

<script>
import {
  FormGroupInput as FgInput,
  Pagination as LPagination,
} from "src/components/index";
import { Select, Option, Table, TableColumn, Input, Dialog, DatePicker } from "element-ui";
import API from "@/services/api";
import moment from "moment";

export default {
  name: "IssuesManagement",
  components: {
    LPagination,
    "el-select": Select,
    "el-option": Option,
    "el-table": Table,
    "el-table-column": TableColumn,
    "el-input": Input,
    ElDialog: Dialog,
    "el-date-picker": DatePicker,
  },
  data() {
    return {
      showIssueForm: false,
      issueService: new API(process.env.VUE_APP_API_URL, "report"),
      issueReportService: new API(process.env.VUE_APP_API_URL, "issues/report"),
      customerService: new API(process.env.VUE_APP_API_URL, "customers"),
      searchByNameCustomerService: new API(
        process.env.VUE_APP_API_URL,
        "customers/search"
      ),
      searchByIdCustomerService: new API(
        process.env.VUE_APP_API_URL,
        "customers"
      ),
      serviceService: new API(process.env.VUE_APP_API_URL, "services"),
      departmentService: new API(process.env.VUE_APP_API_URL, "departments"),
      searchByValueCustomerService: new API(
        process.env.VUE_APP_API_URL,
        "customers/search"
      ),
      searchByAccountsByCustomerIdService: new API(
        process.env.VUE_APP_API_URL,
        "accounts"
      ),
      searchByAccountCustomerService: new API(
        process.env.VUE_APP_API_URL,
        "customers/account"
      ),
      productService: new API(
        process.env.VUE_APP_API_URL,
        "Customers/Product"
      ),
      issuesLogService: new API(process.env.VUE_APP_API_URL, "issuelogs"),
      issueUpdateService: new API(process.env.VUE_APP_API_URL, "issues"),
      assignmentService: new API(process.env.VUE_APP_API_URL, "assignments"),
      searchInput: "",
      searchResults: [],
      selectedCustomer: null,
      btnloading: false,
      searchBy: "name",
      // Search and Pagination
      pagination: {
        perPage: 5,
        currentPage: 1,
        perPageOptions: [5, 10, 15],
        total: 0,
      },
      loading: false,
      searchQuery: "",

      // Table Columns
      tableColumns: [
        { prop: "issueRef", label: "Issue ID", minWidth: 120 },
        {
          prop: "customer",
          label: "Customer",
          minWidth: 140,
        },
        {
          prop: "account",
          label: "Account",
          minWidth: 100,
          formatter: this.formatAccount,
        },
        { prop: "title", label: "Title", minWidth: 200 },
        { prop: "location", label: "Location", minWidth: 150 },
        { prop: "email", label: "Email", minWidth: 210 },
        { prop: "phone", label: "Phone", minWidth: 140 },
        { prop: "description", label: "Details", minWidth: 250 },
        {
          prop: "serviceId",
          label: "Service",
          minWidth: 90,
          formatter: this.formatService,
        },
        { prop: "source", label: "Source", minWidth: 100 },
        { prop: "priority", label: "Priority", minWidth: 80 },
        {
          prop: "occuredAt",
          label: "Occurred At",
          minWidth: 120,
          formatter: this.formatDateTime,
        },
        {
          prop: "reportedAt",
          label: "Reported At",
          minWidth: 120,
          formatter: this.formatDateTime,
        },
      ],
      // Dropdown Options
      issueCategories: [
        "Service Requests",
        "Incidents",
        "Problems",
        "Changes",
        "Billing Issues",
        "call-center Support",
        "Customer Feedback",
        "Account Management",
      ],
      issuePriorities: ["Lowest", "Low", "Medium", "High", "Highest"],
      issueStatuses: ["New", "Categorized", "In Progress"], // Available statuses for filtering

      // Data Arrays
      issues: [],
      customers: [],
      services: [],
      departments: [],
      accounts: [],
      // Current Issue for Form
      currentIssue: {
        issueType: "customer",
        issueId: null,
        customerId: null,
        serviceId: null,
        product: null,
        title: "",
        description: "",
        category: "Other",
        priority: "Medium",
        status: "New",
        district: "",
        assignedDepartmentId: null,
        assignedTechnicianId: null,
        faultStartDate: null,
      },

      // Malawi Districts
      malawiDistricts: [
        "Balaka",
        "Blantyre",
        "Chikwawa",
        "Chiradzulu",
        "Chitipa",
        "Dedza",
        "Dowa",
        "Karonga",
        "Kasungu",
        "Likoma",
        "Lilongwe",
        "Machinga",
        "Mangochi",
        "Mchinji",
        "Mulanje",
        "Mwanza",
        "Mzimba",
        "Neno",
        "Nkhata Bay",
        "Nkhotakota",
        "Nsanje",
        "Ntcheu",
        "Ntchisi",
        "Phalombe",
        "Rumphi",
        "Salima",
        "Thyolo",
        "Zomba",
      ],
      isSaving: false,
      isClosing: false,
      selectedProduct: null,
      loadingProduct: false,
      assignments: [],
      isEditing: false,
    };
  },
  computed: {
    queriedData() {
      // Only show new issues
      let filtered = this.issues.filter(issue => issue.status === 'New');

      // Apply search filter
      if (this.searchQuery) {
        filtered = filtered.filter((issue) =>
          Object.values(issue).some((value) =>
            value?.toString().toLowerCase().includes(this.searchQuery.toLowerCase())
          )
        );
      }

      // Apply pagination
      return filtered.slice(
        (this.pagination.currentPage - 1) * this.pagination.perPage,
        this.pagination.currentPage * this.pagination.perPage
      );
    },
    total() {
      return this.queriedData.length;
    },
    to() {
      let highBound = this.from + this.pagination.perPage;
      if (this.total < highBound) {
        highBound = this.total;
      }
      return highBound;
    },
    from() {
      return this.pagination.perPage * (this.pagination.currentPage - 1);
    },
    productDisplayValue() {
      if (this.loadingProduct) return "Loading product...";
      if (this.selectedProduct) {
        return `${this.selectedProduct.productName} (${this.selectedProduct.productType})`;
      }
      return "";
    },
    loggedInUser() {
      return JSON.parse(localStorage.getItem("user")) || {};
    },
    userId() {
      return this.loggedInUser?.id;
    },
  },
  methods: {
    async fetchAccountByCustomerId(_customer) {
      this.btnloading = true;
      try {
        let customer = await _customer;
        const input = customer.i_customer;
        if (!input) {
          this.$alert.warning("Please enter a customer Id");
          return;
        }

        const response = await this.searchByAccountsByCustomerIdService.getById(
          input
        );
        this.accounts = response;
      } catch (error) {
        this.$alert.error(
          error.response?.data?.message || "Failed to find accounts"
        );
      } finally {
        this.btnloading = false;
      }
    },
    async searchCustomer() {
      this.btnloading = true;
      try {
        const input = this.searchInput.trim();
        if (!input) {
          this.$alert.warning("Please enter a customer " + searchBy);
          return;
        }

        // Check if input is a number (ID or Account)
        if (this.searchBy == "customerid") {
          // Search by ID or Account
          const response = await this.searchByIdCustomerService.getById(input);
          this.populateCustomerDetails(response);
        } else if (this.searchBy == "account") {
          // Search by ID or Account
          const response = await this.searchByAccountCustomerService.getById(
            input
          );
          this.populateCustomerDetails(response);
        } else {
          // Search by Name
          const response = await this.searchByValueCustomerService.getById(
            input + "_" + this.searchBy
          );
          this.searchResults = response;
        }
      } catch (error) {
        this.$alert.error(
          error.response?.data?.message || "Failed to search for customer"
        );
      } finally {
        this.btnloading = false;
      }
    },
    onCustomerSelect(customerId) {
      const customer = this.searchResults.find(
        (c) => c.i_customer === customerId
      );
      if (customer) {
        this.populateCustomerDetails(customer);
        this.fetchAccountByCustomerId(customer);
      }
    },
    async onAccountSelect(accountId) {
      if (!accountId) {
        this.selectedProduct = null;
        this.currentIssue.serviceId = null;
        return;
      }

      this.loadingProduct = true;
      try {
        const response = await this.productService.getById(accountId);
        if (response && response.length > 0) {
          this.selectedProduct = response[0];
          this.currentIssue.product = this.selectedProduct.productName;

          // Auto-assign service based on product
          const matchingService = this.services.find(
            service => service.name.toLowerCase().includes(this.selectedProduct.productType.toLowerCase())
          );

          if (matchingService) {
            this.currentIssue.serviceId = matchingService.i_service_type;
          }
        } else {
          this.selectedProduct = null;
          this.currentIssue.serviceId = null;
        }
      } catch (error) {
        console.error("Failed to fetch product:", error);
        this.selectedProduct = null;
        this.currentIssue.serviceId = null;
      } finally {
        this.loadingProduct = false;
      }
    },

    populateCustomerDetails(customer) {
      this.currentIssue.customerId = customer.i_customer;
      this.currentIssue.customer = customer.firstname + " " + customer.lastname;
      this.currentIssue.company = customer.name;

      if (!this.currentIssue.email) {
        this.currentIssue.email = customer.email;
      }
      if (!this.currentIssue.phone) {
        this.currentIssue.phone = customer.phone1 || customer.phone2;
      }
      if (!this.currentIssue.location) {
        this.currentIssue.location = customer.city;
      }
      // Clear search results after selection
      this.searchResults = [];
    },
    formatDateTime(row, column, cellValue) {
      if (!cellValue) return "";
      return new Date(cellValue).toLocaleString();
    },
    formatCustomer(row, column, cellValue) {
      const customer = this.customers.find((c) => c.customerId === cellValue);
      return customer ? customer.name : "Unknown";
    },
    formatAccount(row, column, cellValue) {
      const customer = this.customers.find(
        (c) => c.customerId === row.customerId
      );
      return customer ? customer.account : "Unknown";
    },
    formatService(row, column, cellValue) {
      const service = this.services.find((s) => s.serviceId === cellValue);
      return service ? service.serviceName : "Unknown";
    },

    // Fetch Methods
    async fetchIssues() {
      this.loading = true;
      try {
        const response = await this.issueService.getAll();
        this.issues = response || [];
        this.pagination.total = response.length || 0;
      } catch (error) {
        this.$alert.error(error.response?.message || "Failed to fetch Issues");
      } finally {
        this.loading = false;
      }
    },
    async fetchCustomers() {
      try {
        this.customers = await this.customerService.getAll();
      } catch (error) {
        this.$alert.error("Failed to fetch Customers");
      }
    },
    async fetchServices() {
      try {
        this.services = (await this.serviceService.getAll())?.services ?? [];
      } catch (error) {
        this.$alert.error("Failed to fetch Services");
      }
    },
    async fetchDepartments() {
      try {
        const response = await this.departmentService.getAll();
        this.departments = Array.isArray(response) ? response : [];

        this.departments = this.departments.reduce((acc, current) => {
          // Check if the department name already exists in the accumulator
          const isDuplicate = acc.some((item) => item.name === current.name);
          // If not a duplicate, add it to the accumulator
          if (!isDuplicate) {
            acc.push(current);
          }
          return acc;
        }, []);
      } catch (error) {
        this.$alert.error("Failed to fetch Departments");
      }
    },

    // Form Methods
    resetCurrentIssue() {
      this.currentIssue = {
        issueType: "customer",
        issueId: null,
        customerId: null,
        serviceId: null,
        product: null,
        account: 1,
        title: "",
        description: "",
        category: "Other",
        priority: "Medium",
        status: "New",
        district: "",
        assignedDepartmentId: null,
        assignedTechnicianId: null,
        faultStartDate: null,
      };
    },
    editIssue(row) {
      this.currentIssue = { ...row };
      this.isEditing = true;
      this.showIssueForm = true;
    },
    closeIssueForm() {
      this.resetCurrentIssue();
      this.isEditing = false;
      this.showIssueForm = false;
      this.selectedProduct = null;
      this.loadingProduct = false;
    },
    async closeIssue() {
      if (this.isClosing) return; // Prevent double submission

      // Confirm close action with higher z-index to appear above the dialog
      const confirmed = await this.$confirm(
        'Are you sure you want to close this issue? This action cannot be undone.',
        'Confirm Close Issue',
        {
          confirmButtonText: 'Yes, Close Issue',
          cancelButtonText: 'Cancel',
          type: 'warning',
          confirmButtonClass: 'btn-danger',
          customClass: 'close-issue-confirm-dialog',
          zIndex: 9999 // Ensure it appears above the issue form dialog
        }
      ).catch(() => false);

      if (!confirmed) return;

      this.isClosing = true;
      try {
        // Auto-assign department and technician if not already assigned
        if (!this.currentIssue.assignedDepartmentId) {
          this.currentIssue.assignedDepartmentId = this.loggedInUser?.departmentId;
        }
        if (!this.currentIssue.assignedTechnicianId) {
          this.currentIssue.assignedTechnicianId = this.loggedInUser?.id;
        }

        // Set issue status to closed
        this.currentIssue.status = "Closed";
        this.currentIssue.visibleToDepartment = false;
        this.currentIssue.isClosed = true;

        // Update the issue
        await this.issueUpdateService.update(
          this.currentIssue.issueId,
          this.currentIssue
        );

        // Create issue log entry
        await this.issuesLogService.create({
          issueId: this.currentIssue.issueId,
          actionTaken: "Issue Closed",
          comment: `Issue closed by ${this.loggedInUser?.fullName}. Status: Closed`,
          status: "Closed",
          updatedAt: moment().format(),
          updatedBy: this.userId,
          expose: false
        });

        // Close any pending assignments
        const pendingAssignments = this.assignments.filter(
          (a) => a.issueId === this.currentIssue.issueId && !a.closedAt
        );

        await Promise.all(
          pendingAssignments.map((assignment) =>
            this.assignmentService.update(assignment.id, {
              ...assignment,
              closedAt: new Date(),
              status: "Closed",
            })
          )
        );

        this.$alert.success("Issue closed successfully");
        this.closeIssueForm();
        await this.loadall();
      } catch (error) {
        this.$alert.error(
          error.response?.data?.message || "Failed to close issue"
        );
      } finally {
        this.isClosing = false;
      }
    },

    async submitIssue() {
      if (this.isSaving) return; // Prevent double submission
      this.isSaving = true;
      try {
        await this.addIssue();
      } finally {
        this.isSaving = false;
      }
    },

    async addIssue() {
      try {
        const newObject = { ...this.currentIssue };
        newObject.issueId;
        if (newObject.assignedDepartmentId) {
          newObject.assignedDate = moment().format();
        } else {
          newObject.assignedDepartmentId = 0;
        }

        newObject.source = "Website";
        newObject.issueType = "customer";

        if (!newObject.account) {
          newObject.account = 1;
        }

        await this.issueReportService.create(newObject);
        this.$alert.success("Issue categorized successfully");
        this.closeIssueForm();
        await this.loadall();
      } catch (error) {
        this.$alert.error(
          error.response?.data?.message || "Failed to categorize Issue"
        );
      }
    },
    loadall() {
      this.fetchIssues();
      this.fetchServices();
      this.fetchDepartments();
    },
  },
  created() {
    this.loadall();
  },
};
</script>

<style scoped>
/* Add custom styles if needed */
</style>

<style>
/* Ensure the close issue confirmation dialog appears above all other dialogs */
.close-issue-confirm-dialog {
  z-index: 9999 !important;
}

.close-issue-confirm-dialog .el-message-box {
  z-index: 9999 !important;
}

.close-issue-confirm-dialog .el-overlay {
  z-index: 9998 !important;
}

/* Ensure the confirmation dialog backdrop covers everything */
.el-message-box__wrapper.close-issue-confirm-dialog {
  z-index: 9999 !important;
}
</style>